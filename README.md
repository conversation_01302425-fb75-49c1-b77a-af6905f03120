# FreeFrame

Free, open source video processing app built with Flutter and powered by FFmpeg.

## Features

- **Cross-platform**: iOS, Android, macOS, Windows, Linux, Web
- **Modern UI**: Dark theme with Material 3 design
- **Multiple modes**: Normal and advanced user interfaces
- **Local processing**: All video processing happens on your device
- **No tracking**: Privacy-focused with no data collection

## Video Processing Features

### Normal Mode
- Format conversion (MP4, WebM, MOV, MKV, AVI)
- Resolution presets (1080p, 720p, 480p, 360p)
- Quality presets (High, Medium, Low)
- Audio options (Keep, Remove, Convert)
- Basic compression

### Advanced Mode
- Custom FFmpeg commands
- Video codec selection (H.264, H.265, VP9, AV1)
- Audio codec selection (AAC, MP3, Opus, Vorbis)
- CRF quality control
- Encoding presets
- Two-pass encoding
- Hardware acceleration (where supported)

## Platform Support

| Platform | FFmpeg Implementation | Status |
|----------|----------------------|---------|
| Android  | ffmpeg_kit_flutter_new | ✅ Ready |
| iOS      | ffmpeg_kit_flutter_new | ✅ Ready |
| macOS    | ffmpeg_kit_flutter_new | ✅ Ready |
| Windows  | ffmpeg_kit_flutter_new | ✅ Ready |
| Linux    | ffmpeg_kit_flutter_new | ✅ Ready |
| Web      | ffmpeg_wasm | ✅ Ready |

## Quick Start

1. **Install dependencies**:
   ```bash
   flutter pub get
   ```

2. **Run the app**:
   ```bash
   flutter run
   ```

## Platform Setup

### Android
- Permissions configured for file access and media access
- Supports Android 13+ granular media permissions
- Large binary considerations for Play Store

### iOS
- Photo library access permissions configured
- File association for video files
- Camera and microphone permissions (if needed)

### Web
- Cross-Origin headers configured for SharedArrayBuffer
- FFmpeg WASM integration ready
- Memory limitations apply

### Desktop (macOS/Windows/Linux)
- FFmpeg binary bundled with app
- File system access configured
- Hardware acceleration available

## Dependencies

### Core Libraries
- [ffmpeg_kit_flutter_new](https://pub.dev/packages/ffmpeg_kit_flutter_new) - FFmpeg for native platforms
- [ffmpeg_wasm](https://pub.dev/packages/ffmpeg_wasm) - FFmpeg for web
- [get](https://pub.dev/packages/get) - State management and routing

### Utility Libraries
- file_picker - File selection
- permission_handler - Runtime permissions
- path_provider - File system paths
- url_launcher - Open URLs and files
- package_info_plus - App metadata
- share_plus - Cross-platform sharing

## License

This project uses LGPL builds of FFmpeg to allow wider distribution. See the Credits page in the app for full license information.

## Contributing

This is a free and open source project. Contributions are welcome!

## Privacy

- All video processing is done locally on your device
- No user files are uploaded to external servers
- No tracking or analytics
- No data collection
