# FreeFrame Setup Guide

## Platform-Specific Setup Instructions

### Android Setup

1. **Permissions**: Already configured in `android/app/src/main/AndroidManifest.xml`
   - READ_EXTERNAL_STORAGE
   - WRITE_EXTERNAL_STORAGE  
   - READ_MEDIA_VIDEO (Android 13+)
   - READ_MEDIA_AUDIO (Android 13+)

2. **Runtime Permission Handling**:
   ```dart
   // Example permission request
   import 'package:permission_handler/permission_handler.dart';
   
   Future<bool> requestStoragePermission() async {
     if (await Permission.storage.isGranted) {
       return true;
     }
     
     final status = await Permission.storage.request();
     return status == PermissionStatus.granted;
   }
   ```

3. **Play Store Considerations**:
   - App size will be large due to FFmpeg binary
   - Consider using Android App Bundle (AAB) format
   - Test on various Android versions

### iOS Setup

1. **Info.plist**: Already configured in `ios/Runner/Info.plist`
   - NSPhotoLibraryUsageDescription
   - NSCameraUsageDescription
   - NSMicrophoneUsageDescription

2. **File Association**: Video files can open with the app

3. **Testing**: Test on both iPhone and iPad

### Web Setup

1. **Server Headers Required**:
   ```
   Cross-Origin-Opener-Policy: same-origin
   Cross-Origin-Embedder-Policy: require-corp
   ```

2. **Apache (.htaccess)**:
   ```apache
   Header always set Cross-Origin-Opener-Policy "same-origin"
   Header always set Cross-Origin-Embedder-Policy "require-corp"
   ```

3. **Nginx**:
   ```nginx
   add_header Cross-Origin-Opener-Policy "same-origin";
   add_header Cross-Origin-Embedder-Policy "require-corp";
   ```

4. **Local Development**:
   ```bash
   flutter run -d chrome --web-browser-flag "--disable-web-security"
   ```

5. **Memory Limitations**:
   - Large files may cause browser crashes
   - Recommend max 100MB files for web
   - Show memory warnings to users

### Desktop Setup (macOS/Windows/Linux)

1. **FFmpeg Binary**: Bundled with app automatically via ffmpeg_kit_flutter_new

2. **File System Access**: Configured for file picker

3. **Hardware Acceleration**: Available on supported systems

## Example FFmpeg Commands

### Basic Conversion
```bash
# Convert MP4 to WebM
ffmpeg -i input.mp4 -c:v libvpx-vp9 -crf 30 -c:a opus output.webm

# Convert to H.264 MP4
ffmpeg -i input.mov -c:v libx264 -crf 23 -c:a aac output.mp4
```

### Resolution Scaling
```bash
# Scale to 720p
ffmpeg -i input.mp4 -vf scale=1280:720 -c:v libx264 -crf 23 output.mp4

# Scale to 480p
ffmpeg -i input.mp4 -vf scale=854:480 -c:v libx264 -crf 23 output.mp4
```

### Compression
```bash
# High compression
ffmpeg -i input.mp4 -c:v libx264 -crf 28 -preset slow output.mp4

# Two-pass encoding
ffmpeg -i input.mp4 -c:v libx264 -b:v 1M -pass 1 -f null /dev/null
ffmpeg -i input.mp4 -c:v libx264 -b:v 1M -pass 2 output.mp4
```

### Audio Options
```bash
# Remove audio
ffmpeg -i input.mp4 -c:v copy -an output.mp4

# Convert audio to AAC
ffmpeg -i input.mp4 -c:v copy -c:a aac output.mp4

# Convert audio to MP3
ffmpeg -i input.mp4 -c:v copy -c:a mp3 output.mp4
```

### Advanced Options
```bash
# Hardware acceleration (NVIDIA)
ffmpeg -hwaccel cuda -i input.mp4 -c:v h264_nvenc -crf 23 output.mp4

# Hardware acceleration (Intel Quick Sync)
ffmpeg -hwaccel qsv -i input.mp4 -c:v h264_qsv -crf 23 output.mp4

# Custom filter chain
ffmpeg -i input.mp4 -vf "scale=1280:720,crop=1280:600:0:60" -c:v libx264 -crf 23 output.mp4
```

## Testing Checklist

### Functional Testing
- [ ] File selection works on all platforms
- [ ] Video conversion completes successfully
- [ ] Progress tracking updates correctly
- [ ] Output files are playable
- [ ] Error handling works for invalid files
- [ ] Memory usage stays reasonable

### Platform Testing
- [ ] Android: Test on different API levels (21+)
- [ ] iOS: Test on iPhone and iPad
- [ ] Web: Test in Chrome, Firefox, Safari
- [ ] macOS: Test on Intel and Apple Silicon
- [ ] Windows: Test on Windows 10/11
- [ ] Linux: Test on Ubuntu/Debian

### Performance Testing
- [ ] Small files (< 10MB) process quickly
- [ ] Medium files (10-100MB) complete without issues
- [ ] Large files (100MB+) show appropriate warnings
- [ ] Memory usage doesn't exceed device limits
- [ ] Battery usage is reasonable on mobile

### Edge Cases
- [ ] Very short videos (< 1 second)
- [ ] Very long videos (> 1 hour)
- [ ] Unusual aspect ratios
- [ ] High resolution videos (4K+)
- [ ] Corrupted or invalid video files
- [ ] Network interruption during processing
- [ ] App backgrounding during processing
- [ ] Low storage space scenarios

### UI/UX Testing
- [ ] Dark theme looks good on all screens
- [ ] Navigation works smoothly
- [ ] Settings persist between app launches
- [ ] Error messages are user-friendly
- [ ] Progress indicators are accurate
- [ ] Responsive layout on different screen sizes

## Performance Notes

### Memory Management
- Clear temporary files after processing
- Monitor memory usage during long operations
- Implement file size limits per platform
- Show memory warnings for large files

### Battery Optimization
- Recommend keeping device plugged in
- Pause processing when battery is low
- Use efficient encoding presets by default

### Storage Management
- Clean up temporary files automatically
- Check available storage before processing
- Compress output when possible

## Troubleshooting

### Common Issues
1. **Permission Denied**: Check file access permissions
2. **Out of Memory**: Reduce file size or quality settings
3. **Slow Processing**: Use faster encoding presets
4. **Web Issues**: Verify CORS headers are set correctly
5. **Audio Sync**: Check audio codec compatibility

### Debug Mode
Enable debug logging in development:
```dart
// Add to main.dart
if (kDebugMode) {
  Get.put(DebugController(), permanent: true);
}
```
