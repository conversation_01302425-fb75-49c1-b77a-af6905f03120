import 'package:get/get.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/convert/bindings/convert_binding.dart';
import '../modules/convert/views/convert_view.dart';
import '../modules/compress/bindings/compress_binding.dart';
import '../modules/compress/views/compress_view.dart';
import '../modules/advanced/bindings/advanced_binding.dart';
import '../modules/advanced/views/advanced_view.dart';
import '../modules/credits/bindings/credits_binding.dart';
import '../modules/credits/views/credits_view.dart';
import '../modules/settings/bindings/settings_binding.dart';
import '../modules/settings/views/settings_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const initial = Routes.home;

  static final routes = [
    GetPage(
      name: _Paths.home,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.convert,
      page: () => const ConvertView(),
      binding: ConvertBinding(),
    ),
    GetPage(
      name: _Paths.compress,
      page: () => const CompressView(),
      binding: CompressBinding(),
    ),
    GetPage(
      name: _Paths.advanced,
      page: () => const AdvancedView(),
      binding: AdvancedBinding(),
    ),
    GetPage(
      name: _Paths.credits,
      page: () => const CreditsView(),
      binding: CreditsBinding(),
    ),
    GetPage(
      name: _Paths.settings,
      page: () => const SettingsView(),
      binding: SettingsBinding(),
    ),
  ];
}
