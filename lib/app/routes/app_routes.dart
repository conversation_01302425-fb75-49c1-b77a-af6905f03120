part of 'app_pages.dart';

abstract class Routes {
  Routes._();
  static const home = _Paths.home;
  static const convert = _Paths.convert;
  static const compress = _Paths.compress;
  static const advanced = _Paths.advanced;
  static const credits = _Paths.credits;
  static const settings = _Paths.settings;
}

abstract class _Paths {
  _Paths._();
  static const home = '/home';
  static const convert = '/convert';
  static const compress = '/compress';
  static const advanced = '/advanced';
  static const credits = '/credits';
  static const settings = '/settings';
}
