import 'package:get/get.dart';
import '../../../controllers/settings_controller.dart' as global_settings;

class SettingsController extends GetxController {
  final global_settings.SettingsController _globalSettings = Get.find();
  
  bool get isAdvancedMode => _globalSettings.isAdvancedMode;
  String get outputQuality => _globalSettings.outputQuality;
  String get defaultFormat => _globalSettings.defaultFormat;
  bool get hardwareAcceleration => _globalSettings.hardwareAcceleration;
  int get maxFileSize => _globalSettings.maxFileSize;
  
  void toggleAdvancedMode() => _globalSettings.toggleAdvancedMode();
  void setOutputQuality(String quality) => _globalSettings.setOutputQuality(quality);
  void setDefaultFormat(String format) => _globalSettings.setDefaultFormat(format);
  void toggleHardwareAcceleration() => _globalSettings.toggleHardwareAcceleration();
  void setMaxFileSize(int size) => _globalSettings.setMaxFileSize(size);
  
  Map<String, String> get qualityPresets => _globalSettings.qualityPresets;
  List<String> get supportedFormats => _globalSettings.supportedFormats;
  
  bool get isWeb => _globalSettings.isWeb;
  bool get isMobile => _globalSettings.isMobile;
  bool get isDesktop => _globalSettings.isDesktop;
}
