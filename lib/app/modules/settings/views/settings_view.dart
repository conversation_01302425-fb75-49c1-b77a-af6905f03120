import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/settings_controller.dart';
import '../../../widgets/bottom_navigation.dart';
import '../../../widgets/dependency_status_widget.dart';
import '../../../services/dependency_checker.dart';
import '../../credits/controllers/credits_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Settings'), centerTitle: true),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // General settings
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'General',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),

                    Obx(
                      () => SwitchListTile(
                        title: Text('Advanced Mode'),
                        subtitle: Text(
                          'Show advanced options and custom commands',
                        ),
                        value: controller.isAdvancedMode,
                        onChanged: (_) => controller.toggleAdvancedMode(),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),

                    SizedBox(height: 16),

                    Text('Default Output Quality'),
                    SizedBox(height: 8),
                    Obx(
                      () => DropdownButtonFormField<String>(
                        initialValue: controller.outputQuality,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        items: controller.qualityPresets.entries
                            .map(
                              (entry) => DropdownMenuItem(
                                value: entry.key,
                                child: Text(entry.value),
                              ),
                            )
                            .toList(),
                        onChanged: (value) =>
                            controller.setOutputQuality(value!),
                      ),
                    ),

                    SizedBox(height: 16),

                    Text('Default Output Format'),
                    SizedBox(height: 8),
                    Obx(
                      () => DropdownButtonFormField<String>(
                        initialValue: controller.defaultFormat,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        items: controller.supportedFormats
                            .map(
                              (format) => DropdownMenuItem(
                                value: format,
                                child: Text(format.toUpperCase()),
                              ),
                            )
                            .toList(),
                        onChanged: (value) =>
                            controller.setDefaultFormat(value!),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Performance settings
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Performance',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),

                    if (!controller.isWeb) ...[
                      Obx(
                        () => SwitchListTile(
                          title: Text('Hardware Acceleration'),
                          subtitle: Text('Use GPU acceleration when available'),
                          value: controller.hardwareAcceleration,
                          onChanged: (_) =>
                              controller.toggleHardwareAcceleration(),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),

                      SizedBox(height: 16),
                    ],

                    Text('Max File Size (MB): ${controller.maxFileSize}'),
                    SizedBox(height: 8),
                    Obx(
                      () => Slider(
                        value: controller.maxFileSize.toDouble(),
                        min: 50,
                        max: 2000,
                        divisions: 39,
                        label: '${controller.maxFileSize} MB',
                        onChanged: (value) =>
                            controller.setMaxFileSize(value.toInt()),
                      ),
                    ),

                    Text(
                      'Larger files may cause memory issues on mobile devices',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Platform info
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Platform Information',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),

                    ListTile(
                      leading: Icon(Icons.devices),
                      title: Text('Platform'),
                      subtitle: Text(
                        controller.isWeb
                            ? 'Web (FFmpeg WASM)'
                            : controller.isMobile
                            ? 'Mobile (FFmpeg Kit)'
                            : 'Desktop (FFmpeg Kit)',
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),

                    if (controller.isWeb) ...[
                      ListTile(
                        leading: Icon(Icons.info_outline),
                        title: Text('Web Limitations'),
                        subtitle: Text(
                          'Processing may be slower and limited by browser memory',
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],

                    if (controller.isMobile) ...[
                      ListTile(
                        leading: Icon(Icons.battery_alert),
                        title: Text('Mobile Tips'),
                        subtitle: Text(
                          'Keep device plugged in for long processing tasks',
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // System dependencies section
            _buildDependenciesSection(context),

            SizedBox(height: 16),

            // Credits section
            _buildCreditsSection(context),

            SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }

  Widget _buildDependenciesSection(BuildContext context) {
    final dependencyChecker = Get.find<DependencyChecker>();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Dependencies',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),

            DependencyStatusWidget(showOnlyIfIssues: false),

            SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => dependencyChecker.checkDependencies(),
                icon: Icon(Icons.refresh),
                label: Text('Check Dependencies'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreditsSection(BuildContext context) {
    final CreditsController creditsController = Get.put(CreditsController());

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About & Credits',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),

            // App info
            ListTile(
              leading: Icon(Icons.info_outline),
              title: Text('FreeFrame'),
              subtitle: Text(
                'Free, open source video processing app\nBuilt with Flutter and powered by FFmpeg',
              ),
              contentPadding: EdgeInsets.zero,
              isThreeLine: true,
            ),

            SizedBox(height: 16),

            // FFmpeg attribution
            ListTile(
              leading: Icon(Icons.video_library, size: 24),
              title: Text(creditsController.ffmpegInfo['name']!),
              subtitle: Text(creditsController.ffmpegInfo['description']!),
              trailing: Icon(Icons.open_in_new, size: 16),
              onTap: () => creditsController.openUrl(
                creditsController.ffmpegInfo['url']!,
              ),
              contentPadding: EdgeInsets.zero,
            ),

            SizedBox(height: 16),

            // View all credits button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showCreditsDialog(context, creditsController),
                icon: Icon(Icons.list),
                label: Text('View All Libraries & Licenses'),
              ),
            ),

            SizedBox(height: 16),

            // Legal notice
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Legal Notice',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  SizedBox(height: 4),
                  Text(
                    'This app is free and open source. All processing is done locally on your device. '
                    'No user files are uploaded to external servers without explicit consent.',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCreditsDialog(
    BuildContext context,
    CreditsController creditsController,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Open Source Libraries'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: creditsController.libraries.length,
            itemBuilder: (context, index) {
              final library = creditsController.libraries[index];
              return ListTile(
                title: Text(library['name']!),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(library['description']!),
                    SizedBox(height: 4),
                    Text(
                      'License: ${library['license']!}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                trailing: Icon(Icons.open_in_new, size: 16),
                onTap: () => creditsController.openUrl(library['url']!),
                contentPadding: EdgeInsets.zero,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
