import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/settings_controller.dart';
import '../../../widgets/bottom_navigation.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Settings'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // General settings
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'General',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),
                    
                    Obx(() => SwitchListTile(
                      title: Text('Advanced Mode'),
                      subtitle: Text('Show advanced options and custom commands'),
                      value: controller.isAdvancedMode,
                      onChanged: (_) => controller.toggleAdvancedMode(),
                      contentPadding: EdgeInsets.zero,
                    )),
                    
                    SizedBox(height: 16),
                    
                    Text('Default Output Quality'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.outputQuality,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.qualityPresets.entries.map((entry) =>
                        DropdownMenuItem(
                          value: entry.key,
                          child: Text(entry.value),
                        ),
                      ).toList(),
                      onChanged: (value) => controller.setOutputQuality(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    Text('Default Output Format'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.defaultFormat,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.supportedFormats.map((format) =>
                        DropdownMenuItem(
                          value: format,
                          child: Text(format.toUpperCase()),
                        ),
                      ).toList(),
                      onChanged: (value) => controller.setDefaultFormat(value!),
                    )),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Performance settings
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Performance',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),
                    
                    if (!controller.isWeb) ...[
                      Obx(() => SwitchListTile(
                        title: Text('Hardware Acceleration'),
                        subtitle: Text('Use GPU acceleration when available'),
                        value: controller.hardwareAcceleration,
                        onChanged: (_) => controller.toggleHardwareAcceleration(),
                        contentPadding: EdgeInsets.zero,
                      )),
                      
                      SizedBox(height: 16),
                    ],
                    
                    Text('Max File Size (MB): ${controller.maxFileSize}'),
                    SizedBox(height: 8),
                    Obx(() => Slider(
                      value: controller.maxFileSize.toDouble(),
                      min: 50,
                      max: 2000,
                      divisions: 39,
                      label: '${controller.maxFileSize} MB',
                      onChanged: (value) => controller.setMaxFileSize(value.toInt()),
                    )),
                    
                    Text(
                      'Larger files may cause memory issues on mobile devices',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Platform info
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Platform Information',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),
                    
                    ListTile(
                      leading: Icon(Icons.devices),
                      title: Text('Platform'),
                      subtitle: Text(
                        controller.isWeb ? 'Web (FFmpeg WASM)' :
                        controller.isMobile ? 'Mobile (FFmpeg Kit)' :
                        'Desktop (FFmpeg Kit)',
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                    
                    if (controller.isWeb) ...[
                      ListTile(
                        leading: Icon(Icons.info_outline),
                        title: Text('Web Limitations'),
                        subtitle: Text('Processing may be slower and limited by browser memory'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                    
                    if (controller.isMobile) ...[
                      ListTile(
                        leading: Icon(Icons.battery_alert),
                        title: Text('Mobile Tips'),
                        subtitle: Text('Keep device plugged in for long processing tasks'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }
}
