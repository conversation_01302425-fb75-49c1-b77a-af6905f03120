import 'package:get/get.dart';

class CompressController extends GetxController {
  final _targetSize = 50.0.obs; // MB
  final _compressionLevel = 'medium'.obs;
  final _maintainAspectRatio = true.obs;
  
  double get targetSize => _targetSize.value;
  String get compressionLevel => _compressionLevel.value;
  bool get maintainAspectRatio => _maintainAspectRatio.value;
  
  void setTargetSize(double size) {
    _targetSize.value = size;
  }
  
  void setCompressionLevel(String level) {
    _compressionLevel.value = level;
  }
  
  void toggleMaintainAspectRatio() {
    _maintainAspectRatio.value = !_maintainAspectRatio.value;
  }
  
  List<String> get compressionLevels => [
    'low',
    'medium', 
    'high',
    'maximum',
  ];
}
