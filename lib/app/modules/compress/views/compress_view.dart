import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/compress_controller.dart';
import '../../../controllers/video_processing_controller.dart';
import '../../../widgets/bottom_navigation.dart';
import '../../../widgets/upload_area.dart';
import '../../../widgets/processing_panel.dart';

class CompressView extends GetView<CompressController> {
  const CompressView({super.key});

  @override
  Widget build(BuildContext context) {
    final VideoProcessingController videoController = Get.find<VideoProcessingController>();
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Compress Video'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UploadArea(),
            SizedBox(height: 24),
            
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Compression Settings',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),
                    
                    Text('Target File Size (MB)'),
                    SizedBox(height: 8),
                    Obx(() => Slider(
                      value: controller.targetSize,
                      min: 1,
                      max: 500,
                      divisions: 100,
                      label: '${controller.targetSize.toInt()} MB',
                      onChanged: controller.setTargetSize,
                    )),
                    
                    SizedBox(height: 16),
                    
                    Text('Compression Level'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.compressionLevel,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.compressionLevels.map((level) =>
                        DropdownMenuItem(
                          value: level,
                          child: Text(level.capitalize!),
                        ),
                      ).toList(),
                      onChanged: (value) => controller.setCompressionLevel(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    Obx(() => CheckboxListTile(
                      title: Text('Maintain Aspect Ratio'),
                      value: controller.maintainAspectRatio,
                      onChanged: (_) => controller.toggleMaintainAspectRatio(),
                      contentPadding: EdgeInsets.zero,
                    )),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            Obx(() => SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: videoController.hasSelectedFile && !videoController.isProcessing
                  ? () => _startCompression(videoController)
                  : null,
                icon: Icon(videoController.isProcessing ? Icons.hourglass_empty : Icons.compress),
                label: Text(videoController.isProcessing ? 'Compressing...' : 'Start Compression'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            )),
            
            SizedBox(height: 24),
            ProcessingPanel(),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }
  
  void _startCompression(VideoProcessingController videoController) {
    videoController.processVideo(
      outputFormat: 'mp4',
      customOptions: {
        'targetSize': controller.targetSize,
        'compressionLevel': controller.compressionLevel,
        'maintainAspectRatio': controller.maintainAspectRatio,
      },
    );
  }
}
