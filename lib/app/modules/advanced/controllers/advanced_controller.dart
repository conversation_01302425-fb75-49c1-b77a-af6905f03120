import 'package:get/get.dart';

class AdvancedController extends GetxController {
  final _customCommand = ''.obs;
  final _videoCodec = 'libx264'.obs;
  final _audioCodec = 'aac'.obs;
  final _crf = 23.obs;
  final _preset = 'medium'.obs;
  final _twoPass = false.obs;
  
  String get customCommand => _customCommand.value;
  String get videoCodec => _videoCodec.value;
  String get audioCodec => _audioCodec.value;
  int get crf => _crf.value;
  String get preset => _preset.value;
  bool get twoPass => _twoPass.value;
  
  void setCustomCommand(String command) {
    _customCommand.value = command;
  }
  
  void setVideoCodec(String codec) {
    _videoCodec.value = codec;
  }
  
  void setAudioCodec(String codec) {
    _audioCodec.value = codec;
  }
  
  void setCrf(int value) {
    _crf.value = value;
  }
  
  void setPreset(String presetValue) {
    _preset.value = presetValue;
  }
  
  void toggleTwoPass() {
    _twoPass.value = !_twoPass.value;
  }
  
  List<String> get videoCodecs => [
    'libx264',
    'libx265',
    'libvpx-vp9',
    'libaom-av1',
  ];
  
  List<String> get audioCodecs => [
    'aac',
    'mp3',
    'opus',
    'vorbis',
  ];
  
  List<String> get presets => [
    'ultrafast',
    'superfast',
    'veryfast',
    'faster',
    'fast',
    'medium',
    'slow',
    'slower',
    'veryslow',
  ];
  
  String generateCommand() {
    if (customCommand.isNotEmpty) {
      return customCommand;
    }
    
    List<String> parts = [
      'ffmpeg',
      '-i input.mp4',
      '-c:v $videoCodec',
      '-crf $crf',
      '-preset $preset',
      '-c:a $audioCodec',
    ];
    
    if (twoPass) {
      parts.add('-pass 1');
    }
    
    parts.add('output.mp4');
    
    return parts.join(' ');
  }
}
