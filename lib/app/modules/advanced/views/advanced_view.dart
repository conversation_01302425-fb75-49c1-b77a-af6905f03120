import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/advanced_controller.dart';
import '../../../controllers/video_processing_controller.dart';
import '../../../widgets/bottom_navigation.dart';
import '../../../widgets/upload_area.dart';
import '../../../widgets/processing_panel.dart';

class AdvancedView extends GetView<AdvancedController> {
  const AdvancedView({super.key});

  @override
  Widget build(BuildContext context) {
    final VideoProcessingController videoController = Get.find<VideoProcessingController>();
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Advanced Options'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UploadArea(),
            SizedBox(height: 24),
            
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Advanced Settings',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),
                    
                    Text('Video Codec'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.videoCodec,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.videoCodecs.map((codec) =>
                        DropdownMenuItem(value: codec, child: Text(codec)),
                      ).toList(),
                      onChanged: (value) => controller.setVideoCodec(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    Text('Audio Codec'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.audioCodec,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.audioCodecs.map((codec) =>
                        DropdownMenuItem(value: codec, child: Text(codec)),
                      ).toList(),
                      onChanged: (value) => controller.setAudioCodec(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    Text('CRF (Quality): ${controller.crf}'),
                    SizedBox(height: 8),
                    Obx(() => Slider(
                      value: controller.crf.toDouble(),
                      min: 0,
                      max: 51,
                      divisions: 51,
                      onChanged: (value) => controller.setCrf(value.toInt()),
                    )),
                    
                    SizedBox(height: 16),
                    
                    Text('Preset'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.preset,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.presets.map((preset) =>
                        DropdownMenuItem(value: preset, child: Text(preset)),
                      ).toList(),
                      onChanged: (value) => controller.setPreset(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    Obx(() => CheckboxListTile(
                      title: Text('Two-Pass Encoding'),
                      value: controller.twoPass,
                      onChanged: (_) => controller.toggleTwoPass(),
                      contentPadding: EdgeInsets.zero,
                    )),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Command Preview',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    SizedBox(height: 8),
                    Obx(() => Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        controller.generateCommand(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontFamily: 'monospace',
                        ),
                      ),
                    )),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            Obx(() => SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: videoController.hasSelectedFile && !videoController.isProcessing
                  ? () => _startAdvancedProcessing(videoController)
                  : null,
                icon: Icon(videoController.isProcessing ? Icons.hourglass_empty : Icons.play_arrow),
                label: Text(videoController.isProcessing ? 'Processing...' : 'Start Processing'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            )),
            
            SizedBox(height: 24),
            ProcessingPanel(),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }
  
  void _startAdvancedProcessing(VideoProcessingController videoController) {
    videoController.processVideo(
      outputFormat: 'mp4',
      customOptions: {
        'videoCodec': controller.videoCodec,
        'audioCodec': controller.audioCodec,
        'crf': controller.crf,
        'preset': controller.preset,
        'twoPass': controller.twoPass,
        'customCommand': controller.customCommand,
      },
    );
  }
}
