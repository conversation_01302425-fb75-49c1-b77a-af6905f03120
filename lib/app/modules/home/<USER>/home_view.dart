import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';
import '../../../controllers/navigation_controller.dart';
import '../../../widgets/bottom_navigation.dart';
import '../../../widgets/upload_area.dart';
import '../../../core/constants/app_constants.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('FreeFrame'), centerTitle: true),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            Card(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to FreeFrame',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Free, open source video processing with FFmpeg',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 24),

            // Upload area
            UploadArea(),

            SizedBox(height: 24),

            // Quick actions
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),

            LayoutBuilder(
              builder: (context, constraints) {
                // Responsive grid based on screen width
                int crossAxisCount = constraints.maxWidth > 600 ? 4 : 2;
                double childAspectRatio = constraints.maxWidth > 600
                    ? 1.2
                    : 1.5;

                return GridView.count(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: AppConstants.spacingM,
                  mainAxisSpacing: AppConstants.spacingM,
                  childAspectRatio: childAspectRatio,
                  children: [
                    _buildQuickActionCard(
                      context,
                      'Convert',
                      Icons.transform,
                      'Change video format',
                      () => Get.find<NavigationController>().navigateToTab(1),
                    ),
                    _buildQuickActionCard(
                      context,
                      'Compress',
                      Icons.compress,
                      'Reduce file size',
                      () => Get.find<NavigationController>().navigateToTab(2),
                    ),
                    _buildQuickActionCard(
                      context,
                      'Advanced',
                      Icons.tune,
                      'Custom options',
                      () => Get.find<NavigationController>().navigateToTab(3),
                    ),
                    _buildQuickActionCard(
                      context,
                      'Settings',
                      Icons.settings,
                      'App preferences',
                      () => Get.find<NavigationController>().navigateToTab(4),
                    ),
                  ],
                );
              },
            ),

            SizedBox(height: 24),

            // Recent files
            Obx(() {
              if (controller.recentFiles.isEmpty) {
                return SizedBox.shrink();
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Recent Files',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  SizedBox(height: 16),
                  ...controller.recentFiles.map(
                    (file) => ListTile(
                      leading: Icon(Icons.video_file),
                      title: Text(file.split('/').last),
                      subtitle: Text(file),
                      trailing: Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // Handle recent file selection
                      },
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    String subtitle,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: AppConstants.radiusM.borderRadius,
        child: Semantics(
          label: '$title. $subtitle',
          button: true,
          enabled: true,
          child: Padding(
            padding: AppConstants.cardPadding,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: AppConstants.iconL,
                  semanticLabel: '$title icon',
                ),
                AppConstants.spacingS.verticalSpace,
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                AppConstants.spacingXs.verticalSpace,
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
