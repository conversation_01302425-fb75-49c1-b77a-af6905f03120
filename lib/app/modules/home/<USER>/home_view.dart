import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';
import '../../../controllers/navigation_controller.dart';
import '../../../widgets/bottom_navigation.dart';
import '../../../widgets/upload_area.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('FreeFrame'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            Card(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to FreeFrame',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Free, open source video processing with FFmpeg',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // Upload area
            UploadArea(),
            
            SizedBox(height: 24),
            
            // Quick actions
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            
            GridView.count(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                _buildQuickActionCard(
                  context,
                  'Convert',
                  Icons.transform,
                  'Change video format',
                  () => Get.find<NavigationController>().navigateToTab(1),
                ),
                _buildQuickActionCard(
                  context,
                  'Compress',
                  Icons.compress,
                  'Reduce file size',
                  () => Get.find<NavigationController>().navigateToTab(2),
                ),
                _buildQuickActionCard(
                  context,
                  'Advanced',
                  Icons.tune,
                  'Custom options',
                  () => Get.find<NavigationController>().navigateToTab(3),
                ),
                _buildQuickActionCard(
                  context,
                  'Settings',
                  Icons.settings,
                  'App preferences',
                  () => Get.find<NavigationController>().navigateToTab(5),
                ),
              ],
            ),
            
            SizedBox(height: 24),
            
            // Recent files
            Obx(() {
              if (controller.recentFiles.isEmpty) {
                return SizedBox.shrink();
              }
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Recent Files',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  SizedBox(height: 16),
                  ...controller.recentFiles.map((file) => 
                    ListTile(
                      leading: Icon(Icons.video_file),
                      title: Text(file.split('/').last),
                      subtitle: Text(file),
                      trailing: Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // Handle recent file selection
                      },
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }
  
  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    String subtitle,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32),
              SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
