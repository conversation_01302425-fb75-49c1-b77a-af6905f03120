import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class CreditsController extends GetxController {
  
  Future<void> openUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
  
  List<Map<String, String>> get libraries => [
    {
      'name': 'FFmpeg Kit Flutter',
      'description': 'FFmpeg for native platforms (Android, iOS, macOS, Windows, Linux)',
      'license': 'LGPL v3.0',
      'url': 'https://pub.dev/packages/ffmpeg_kit_flutter_new',
    },
    {
      'name': 'FFmpeg WASM',
      'description': 'FFmpeg for web platform using WebAssembly',
      'license': 'MIT License',
      'url': 'https://pub.dev/packages/ffmpeg_wasm',
    },
    {
      'name': 'GetX',
      'description': 'State management and routing solution',
      'license': 'MIT License',
      'url': 'https://pub.dev/packages/get',
    },
    {
      'name': 'File Picker',
      'description': 'Cross-platform file selection',
      'license': 'MIT License',
      'url': 'https://pub.dev/packages/file_picker',
    },
    {
      'name': 'Permission Handler',
      'description': 'Runtime permission management',
      'license': 'MIT License',
      'url': 'https://pub.dev/packages/permission_handler',
    },
    {
      'name': 'Path Provider',
      'description': 'File system path access',
      'license': 'BSD-3-Clause',
      'url': 'https://pub.dev/packages/path_provider',
    },
    {
      'name': 'URL Launcher',
      'description': 'Launch URLs and files',
      'license': 'BSD-3-Clause',
      'url': 'https://pub.dev/packages/url_launcher',
    },
    {
      'name': 'Package Info Plus',
      'description': 'App metadata access',
      'license': 'BSD-3-Clause',
      'url': 'https://pub.dev/packages/package_info_plus',
    },
    {
      'name': 'Share Plus',
      'description': 'Cross-platform sharing',
      'license': 'BSD-3-Clause',
      'url': 'https://pub.dev/packages/share_plus',
    },
  ];
  
  Map<String, String> get ffmpegInfo => {
    'name': 'FFmpeg',
    'description': 'A complete, cross-platform solution to record, convert and stream audio and video',
    'license': 'LGPL v2.1+ (recommended build) / GPL v2+ (full features)',
    'url': 'https://ffmpeg.org/',
    'note': 'This app uses LGPL builds of FFmpeg to allow wider distribution. Confirm license terms before commercial use.',
  };
}
