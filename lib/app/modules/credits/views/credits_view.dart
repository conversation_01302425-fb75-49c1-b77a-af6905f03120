import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/credits_controller.dart';
import '../../../widgets/bottom_navigation.dart';

class CreditsView extends GetView<CreditsController> {
  const CreditsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Credits & Licenses'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // App info
            Card(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FreeFrame',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Free, open source video processing app',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Built with Flutter and powered by FFmpeg',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // FFmpeg attribution
            Text(
              'Core Technology',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            
            Card(
              child: ListTile(
                leading: Icon(Icons.video_library, size: 32),
                title: Text(controller.ffmpegInfo['name']!),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(controller.ffmpegInfo['description']!),
                    SizedBox(height: 4),
                    Text(
                      'License: ${controller.ffmpegInfo['license']!}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    if (controller.ffmpegInfo['note'] != null) ...[
                      SizedBox(height: 4),
                      Text(
                        controller.ffmpegInfo['note']!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
                trailing: Icon(Icons.open_in_new),
                onTap: () => controller.openUrl(controller.ffmpegInfo['url']!),
              ),
            ),
            
            SizedBox(height: 24),
            
            // Libraries
            Text(
              'Open Source Libraries',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            
            ...controller.libraries.map((library) => Card(
              margin: EdgeInsets.only(bottom: 8),
              child: ListTile(
                title: Text(library['name']!),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(library['description']!),
                    SizedBox(height: 4),
                    Text(
                      'License: ${library['license']!}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                trailing: Icon(Icons.open_in_new),
                onTap: () => controller.openUrl(library['url']!),
              ),
            )),
            
            SizedBox(height: 24),
            
            // Legal notice
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Legal Notice',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This app is free and open source. All processing is done locally on your device. '
                      'No user files are uploaded to external servers without explicit consent. '
                      'Please verify license compatibility before commercial use.',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }
}
