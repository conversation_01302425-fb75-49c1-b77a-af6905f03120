import 'package:get/get.dart';

class ConvertController extends GetxController {
  final _selectedFormat = 'mp4'.obs;
  final _selectedResolution = 'original'.obs;
  final _selectedQuality = 'medium'.obs;
  final _keepAudio = true.obs;
  
  String get selectedFormat => _selectedFormat.value;
  String get selectedResolution => _selectedResolution.value;
  String get selectedQuality => _selectedQuality.value;
  bool get keepAudio => _keepAudio.value;
  
  void setFormat(String format) {
    _selectedFormat.value = format;
  }
  
  void setResolution(String resolution) {
    _selectedResolution.value = resolution;
  }
  
  void setQuality(String quality) {
    _selectedQuality.value = quality;
  }
  
  void toggleKeepAudio() {
    _keepAudio.value = !_keepAudio.value;
  }
  
  List<String> get supportedFormats => [
    'mp4',
    'webm',
    'mov',
    'mkv',
    'avi',
  ];
  
  List<String> get resolutionOptions => [
    'original',
    '1080p',
    '720p',
    '480p',
    '360p',
  ];
  
  List<String> get qualityOptions => [
    'high',
    'medium',
    'low',
  ];
}
