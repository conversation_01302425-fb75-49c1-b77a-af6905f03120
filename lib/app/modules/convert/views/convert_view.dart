import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/convert_controller.dart';
import '../../../controllers/video_processing_controller.dart';
import '../../../widgets/bottom_navigation.dart';
import '../../../widgets/upload_area.dart';
import '../../../widgets/processing_panel.dart';

class ConvertView extends GetView<ConvertController> {
  const ConvertView({super.key});

  @override
  Widget build(BuildContext context) {
    final VideoProcessingController videoController = Get.find<VideoProcessingController>();
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Convert Video'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Upload area
            UploadArea(),
            
            SizedBox(height: 24),
            
            // Conversion options
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Conversion Options',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 16),
                    
                    // Format selection
                    Text('Output Format'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.selectedFormat,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.supportedFormats.map((format) =>
                        DropdownMenuItem(
                          value: format,
                          child: Text(format.toUpperCase()),
                        ),
                      ).toList(),
                      onChanged: (value) => controller.setFormat(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    // Resolution selection
                    Text('Resolution'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.selectedResolution,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.resolutionOptions.map((resolution) =>
                        DropdownMenuItem(
                          value: resolution,
                          child: Text(resolution == 'original' ? 'Original' : resolution),
                        ),
                      ).toList(),
                      onChanged: (value) => controller.setResolution(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    // Quality selection
                    Text('Quality'),
                    SizedBox(height: 8),
                    Obx(() => DropdownButtonFormField<String>(
                      initialValue: controller.selectedQuality,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.qualityOptions.map((quality) =>
                        DropdownMenuItem(
                          value: quality,
                          child: Text(quality.capitalize!),
                        ),
                      ).toList(),
                      onChanged: (value) => controller.setQuality(value!),
                    )),
                    
                    SizedBox(height: 16),
                    
                    // Audio options
                    Obx(() => CheckboxListTile(
                      title: Text('Keep Audio'),
                      value: controller.keepAudio,
                      onChanged: (_) => controller.toggleKeepAudio(),
                      contentPadding: EdgeInsets.zero,
                    )),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // Convert button
            Obx(() => SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: videoController.hasSelectedFile && !videoController.isProcessing
                  ? () => _startConversion(videoController)
                  : null,
                icon: Icon(videoController.isProcessing ? Icons.hourglass_empty : Icons.play_arrow),
                label: Text(videoController.isProcessing ? 'Converting...' : 'Start Conversion'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            )),
            
            SizedBox(height: 24),
            
            // Processing panel
            ProcessingPanel(),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(),
    );
  }
  
  void _startConversion(VideoProcessingController videoController) {
    videoController.processVideo(
      outputFormat: controller.selectedFormat,
      resolution: controller.selectedResolution == 'original' ? null : controller.selectedResolution,
      quality: controller.selectedQuality,
      customOptions: {
        'keepAudio': controller.keepAudio,
      },
    );
  }
}
