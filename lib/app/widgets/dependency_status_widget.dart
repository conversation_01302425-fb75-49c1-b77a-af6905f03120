import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/dependency_checker.dart';
import '../core/constants/app_constants.dart';

class DependencyStatusWidget extends StatelessWidget {
  final bool showOnlyIfIssues;
  
  const DependencyStatusWidget({
    super.key,
    this.showOnlyIfIssues = true,
  });

  @override
  Widget build(BuildContext context) {
    final dependencyChecker = Get.find<DependencyChecker>();
    
    return Obx(() {
      if (dependencyChecker.isChecking) {
        return _buildCheckingWidget();
      }
      
      if (showOnlyIfIssues && dependencyChecker.allDependenciesAvailable) {
        return SizedBox.shrink();
      }
      
      return _buildStatusWidget(dependencyChecker);
    });
  }
  
  Widget _buildCheckingWidget() {
    return Card(
      child: Padding(
        padding: AppConstants.cardPadding,
        child: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            AppConstants.spacingM.horizontalSpace,
            Text('Checking system dependencies...'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatusWidget(DependencyChecker checker) {
    final missingDeps = checker.dependencies
        .where((dep) => dep.status == DependencyStatus.missing)
        .toList();
    
    if (missingDeps.isEmpty) {
      return _buildSuccessWidget();
    }
    
    return _buildWarningWidget(missingDeps, checker);
  }
  
  Widget _buildSuccessWidget() {
    return Card(
      child: Padding(
        padding: AppConstants.cardPadding,
        child: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: AppConstants.iconM,
            ),
            AppConstants.spacingM.horizontalSpace,
            Expanded(
              child: Text(
                'All dependencies are available',
                style: Get.textTheme.bodyMedium?.copyWith(
                  color: Colors.green,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildWarningWidget(List<DependencyInfo> missingDeps, DependencyChecker checker) {
    return Card(
      child: Padding(
        padding: AppConstants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Get.theme.colorScheme.error,
                  size: AppConstants.iconM,
                ),
                AppConstants.spacingM.horizontalSpace,
                Expanded(
                  child: Text(
                    'Missing Dependencies',
                    style: Get.textTheme.titleMedium?.copyWith(
                      color: Get.theme.colorScheme.error,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            AppConstants.spacingS.verticalSpace,
            Text(
              '${missingDeps.length} required component(s) missing. Some features may not work properly.',
              style: Get.textTheme.bodyMedium,
            ),
            AppConstants.spacingM.verticalSpace,
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => checker.showDependencyDialog(),
                    icon: Icon(Icons.info_outline, size: AppConstants.iconS),
                    label: Text('View Details'),
                  ),
                ),
                AppConstants.spacingM.horizontalSpace,
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => checker.checkDependencies(),
                    icon: Icon(Icons.refresh, size: AppConstants.iconS),
                    label: Text('Recheck'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class DependencyStatusBanner extends StatelessWidget {
  const DependencyStatusBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final dependencyChecker = Get.find<DependencyChecker>();
    
    return Obx(() {
      if (dependencyChecker.allDependenciesAvailable || dependencyChecker.isChecking) {
        return SizedBox.shrink();
      }
      
      final missingDeps = dependencyChecker.dependencies
          .where((dep) => dep.status == DependencyStatus.missing)
          .toList();
      
      if (missingDeps.isEmpty) return SizedBox.shrink();
      
      return MaterialBanner(
        backgroundColor: Get.theme.colorScheme.errorContainer,
        content: Text(
          'Some system dependencies are missing. Video processing may not work properly.',
          style: TextStyle(
            color: Get.theme.colorScheme.onErrorContainer,
          ),
        ),
        leading: Icon(
          Icons.warning,
          color: Get.theme.colorScheme.onErrorContainer,
        ),
        actions: [
          TextButton(
            onPressed: () => dependencyChecker.showDependencyDialog(),
            child: Text(
              'Fix',
              style: TextStyle(
                color: Get.theme.colorScheme.onErrorContainer,
              ),
            ),
          ),
        ],
      );
    });
  }
}
