import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/navigation_controller.dart';

class BottomNavigation extends StatelessWidget {
  const BottomNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    final NavigationController navController = Get.find();

    return Obx(
      () => BottomNavigationBar(
        currentIndex: navController.currentIndex,
        onTap: navController.navigateToTab,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Theme.of(
          context,
        ).colorScheme.onSurface.withValues(alpha: 0.6),
        items: [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.transform),
            label: 'Convert',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.compress),
            label: 'Compress',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.tune), label: 'Advanced'),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
