import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/video_processing_controller.dart';

class ProcessingPanel extends StatelessWidget {
  const ProcessingPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final VideoProcessingController controller = Get.find<VideoProcessingController>();
    
    return Obx(() {
      if (controller.processingState == ProcessingState.idle) {
        return SizedBox.shrink();
      }
      
      return Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    controller.processingState == ProcessingState.processing
                      ? Icons.hourglass_empty
                      : controller.processingState == ProcessingState.completed
                        ? Icons.check_circle
                        : Icons.error,
                    color: controller.processingState == ProcessingState.processing
                      ? Theme.of(context).colorScheme.primary
                      : controller.processingState == ProcessingState.completed
                        ? Colors.green
                        : Colors.red,
                  ),
                  SizedBox(width: 8),
                  Text(
                    controller.processingState == ProcessingState.processing
                      ? 'Processing...'
                      : controller.processingState == ProcessingState.completed
                        ? 'Completed'
                        : 'Error',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Spacer(),
                  if (controller.estimatedTime.isNotEmpty)
                    Text(
                      controller.estimatedTime,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                ],
              ),
              
              if (controller.processingState == ProcessingState.processing) ...[
                SizedBox(height: 16),
                LinearProgressIndicator(
                  value: controller.progress,
                  backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                ),
                SizedBox(height: 8),
                Text(
                  '${(controller.progress * 100).toInt()}%',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
              
              if (controller.logs.isNotEmpty) ...[
                SizedBox(height: 16),
                Text(
                  'Processing Log',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                SizedBox(height: 8),
                Container(
                  height: 120,
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      controller.logs.join('\n'),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ),
              ],
              
              if (controller.processingState == ProcessingState.completed && controller.hasOutputFile) ...[
                SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Open output file
                        },
                        icon: Icon(Icons.play_arrow),
                        label: Text('Open Result'),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // Share output file
                        },
                        icon: Icon(Icons.share),
                        label: Text('Share'),
                      ),
                    ),
                  ],
                ),
              ],
              
              if (controller.processingState != ProcessingState.idle) ...[
                SizedBox(height: 8),
                TextButton.icon(
                  onPressed: controller.reset,
                  icon: Icon(Icons.refresh),
                  label: Text('Reset'),
                ),
              ],
            ],
          ),
        ),
      );
    });
  }
}
