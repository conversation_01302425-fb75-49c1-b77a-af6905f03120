import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/video_processing_controller.dart';
import '../core/constants/app_constants.dart';

class UploadArea extends StatelessWidget {
  const UploadArea({super.key});

  @override
  Widget build(BuildContext context) {
    final VideoProcessingController videoController =
        Get.find<VideoProcessingController>();

    return Obx(
      () => Card(
        child: InkWell(
          onTap: videoController.selectVideoFile,
          borderRadius: AppConstants.radiusL.borderRadius,
          child: Semantics(
            label: videoController.hasSelectedFile
                ? 'Video file selected: ${_getFileName(videoController.selectedFile!.path)}. Tap to change file.'
                : 'Upload area. Tap to select a video file for processing.',
            button: true,
            enabled: true,
            child: Container(
              width: double.infinity,
              padding: AppConstants.cardPaddingL,
              constraints: BoxConstraints(
                minHeight: 120,
                maxHeight: MediaQuery.of(context).size.height * 0.3,
              ),
              child: Column(
              children: [
                AnimatedSwitcher(
                  duration: AppConstants.animationMedium,
                  child: Icon(
                    videoController.hasSelectedFile
                        ? Icons.video_file
                        : Icons.cloud_upload_outlined,
                    key: ValueKey(videoController.hasSelectedFile),
                    size: AppConstants.iconXl,
                    color: videoController.hasSelectedFile
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                AppConstants.spacingM.verticalSpace,
                AnimatedSwitcher(
                  duration: AppConstants.animationMedium,
                  child: Text(
                    videoController.hasSelectedFile
                        ? 'File Selected'
                        : 'Select Video File',
                    key: ValueKey('${videoController.hasSelectedFile}_title'),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                AppConstants.spacingS.verticalSpace,
                AnimatedSwitcher(
                  duration: AppConstants.animationMedium,
                  child: Text(
                    videoController.hasSelectedFile
                        ? _getFileName(videoController.selectedFile!.path)
                        : 'Tap to choose a video file to process',
                    key: ValueKey(
                      '${videoController.hasSelectedFile}_subtitle',
                    ),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // Debug info
                if (kDebugMode) ...[
                  SizedBox(height: 8),
                  Text(
                    'Debug: hasSelectedFile=${videoController.hasSelectedFile}, selectedFile=${videoController.selectedFile?.path}',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      // Test button to manually set a file for debugging
                      videoController.setTestFile();
                    },
                    child: Text('Test Set File'),
                  ),
                ],
                if (videoController.hasSelectedFile) ...[
                  AppConstants.spacingM.verticalSpace,
                  Wrap(
                    spacing: AppConstants.spacingM,
                    runSpacing: AppConstants.spacingS,
                    alignment: WrapAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: videoController.selectVideoFile,
                        icon: Icon(Icons.refresh, size: AppConstants.iconS),
                        label: Text('Change File'),
                      ),
                      OutlinedButton.icon(
                        onPressed: videoController.reset,
                        icon: Icon(Icons.clear, size: AppConstants.iconS),
                        label: Text('Clear'),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    )
    );
  }

  String _getFileName(String path) {
    final fileName = path.split('/').last;
    // Truncate long filenames for better display
    if (fileName.length > 30) {
      final extension = fileName.split('.').last;
      final nameWithoutExt = fileName.substring(
        0,
        fileName.length - extension.length - 1,
      );
      return '${nameWithoutExt.substring(0, 25)}...$extension';
    }
    return fileName;
  }
}
