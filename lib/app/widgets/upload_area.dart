import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/video_processing_controller.dart';

class UploadArea extends StatelessWidget {
  const UploadArea({super.key});

  @override
  Widget build(BuildContext context) {
    final VideoProcessingController videoController = Get.find<VideoProcessingController>();
    
    return Obx(() => Card(
      child: InkWell(
        onTap: videoController.selectVideoFile,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                videoController.hasSelectedFile 
                  ? Icons.video_file 
                  : Icons.cloud_upload,
                size: 48,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(height: 16),
              Text(
                videoController.hasSelectedFile
                  ? 'File Selected'
                  : 'Select Video File',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 8),
              Text(
                videoController.hasSelectedFile
                  ? videoController.selectedFile!.path.split('/').last
                  : 'Tap to choose a video file to process',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              // Debug info
              if (kDebugMode) ...[
                SizedBox(height: 8),
                Text(
                  'Debug: hasSelectedFile=${videoController.hasSelectedFile}, selectedFile=${videoController.selectedFile?.path}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () {
                    // Test button to manually set a file for debugging
                    videoController.setTestFile();
                  },
                  child: Text('Test Set File'),
                ),
              ],
              if (videoController.hasSelectedFile) ...[
                SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: videoController.selectVideoFile,
                      icon: Icon(Icons.refresh),
                      label: Text('Change File'),
                    ),
                    SizedBox(width: 16),
                    OutlinedButton.icon(
                      onPressed: videoController.reset,
                      icon: Icon(Icons.clear),
                      label: Text('Clear'),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    ));
  }
}
