import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';

enum DependencyStatus {
  available,
  missing,
  unknown,
}

class DependencyInfo {
  final String name;
  final String description;
  final DependencyStatus status;
  final String? installInstructions;
  final String? downloadUrl;

  DependencyInfo({
    required this.name,
    required this.description,
    required this.status,
    this.installInstructions,
    this.downloadUrl,
  });
}

class DependencyChecker extends GetxService {
  final _ffmpegStatus = DependencyStatus.unknown.obs;
  final _dependencies = <DependencyInfo>[].obs;
  final _isChecking = false.obs;

  DependencyStatus get ffmpegStatus => _ffmpegStatus.value;
  List<DependencyInfo> get dependencies => _dependencies;
  bool get isChecking => _isChecking.value;
  bool get allDependenciesAvailable => 
      _dependencies.every((dep) => dep.status == DependencyStatus.available);

  @override
  Future<void> onInit() async {
    super.onInit();
    await checkDependencies();
  }

  Future<void> checkDependencies() async {
    _isChecking.value = true;
    _dependencies.clear();

    try {
      if (kIsWeb) {
        await _checkWebDependencies();
      } else {
        await _checkNativeDependencies();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking dependencies: $e');
      }
    } finally {
      _isChecking.value = false;
    }
  }

  Future<void> _checkWebDependencies() async {
    // For web, FFmpeg WASM should be available
    _dependencies.add(DependencyInfo(
      name: 'FFmpeg WASM',
      description: 'WebAssembly version of FFmpeg for browser-based video processing',
      status: DependencyStatus.available, // Assume available for web
    ));
    _ffmpegStatus.value = DependencyStatus.available;
  }

  Future<void> _checkNativeDependencies() async {
    // Check FFmpeg availability
    final ffmpegInfo = await _checkFFmpeg();
    _dependencies.add(ffmpegInfo);
    _ffmpegStatus.value = ffmpegInfo.status;

    // Check system libraries
    if (Platform.isMacOS) {
      await _checkMacOSDependencies();
    } else if (Platform.isLinux) {
      await _checkLinuxDependencies();
    } else if (Platform.isWindows) {
      await _checkWindowsDependencies();
    }
  }

  Future<DependencyInfo> _checkFFmpeg() async {
    try {
      // Try to run ffmpeg command to check if it's available
      final result = await Process.run('ffmpeg', ['-version']);
      if (result.exitCode == 0) {
        return DependencyInfo(
          name: 'FFmpeg',
          description: 'Video processing engine',
          status: DependencyStatus.available,
        );
      }
    } catch (e) {
      // FFmpeg not found
    }

    return DependencyInfo(
      name: 'FFmpeg',
      description: 'Video processing engine - Required for video conversion',
      status: DependencyStatus.missing,
      installInstructions: _getFFmpegInstallInstructions(),
      downloadUrl: 'https://ffmpeg.org/download.html',
    );
  }

  Future<void> _checkMacOSDependencies() async {
    // Check for common macOS libraries
    final zlibInfo = await _checkLibrary(
      'zlib',
      '/opt/homebrew/opt/zlib/lib/libz.1.dylib',
      'Compression library required by FFmpeg',
      'brew install zlib',
    );
    _dependencies.add(zlibInfo);

    // Check for other common dependencies
    final x264Info = await _checkLibrary(
      'x264',
      '/opt/homebrew/opt/x264/lib/libx264.dylib',
      'H.264 video encoder',
      'brew install x264',
    );
    _dependencies.add(x264Info);
  }

  Future<void> _checkLinuxDependencies() async {
    // Check for common Linux libraries
    final zlibInfo = await _checkLibrary(
      'zlib',
      '/usr/lib/x86_64-linux-gnu/libz.so.1',
      'Compression library required by FFmpeg',
      'sudo apt-get install zlib1g-dev',
    );
    _dependencies.add(zlibInfo);
  }

  Future<void> _checkWindowsDependencies() async {
    // For Windows, dependencies are usually bundled
    _dependencies.add(DependencyInfo(
      name: 'Windows Runtime',
      description: 'Windows system libraries',
      status: DependencyStatus.available,
    ));
  }

  Future<DependencyInfo> _checkLibrary(
    String name,
    String path,
    String description,
    String installCommand,
  ) async {
    final file = File(path);
    final exists = await file.exists();

    return DependencyInfo(
      name: name,
      description: description,
      status: exists ? DependencyStatus.available : DependencyStatus.missing,
      installInstructions: exists ? null : installCommand,
    );
  }

  String _getFFmpegInstallInstructions() {
    if (Platform.isMacOS) {
      return 'Install using Homebrew:\nbrew install ffmpeg\n\nOr download from: https://ffmpeg.org/download.html';
    } else if (Platform.isLinux) {
      return 'Install using package manager:\nsudo apt-get install ffmpeg\n\nOr: sudo yum install ffmpeg';
    } else if (Platform.isWindows) {
      return 'Download FFmpeg from:\nhttps://ffmpeg.org/download.html\n\nExtract and add to PATH';
    }
    return 'Please install FFmpeg from: https://ffmpeg.org/download.html';
  }

  void showDependencyDialog() {
    if (Get.context == null) return;

    final missingDeps = _dependencies.where((dep) => dep.status == DependencyStatus.missing).toList();
    
    if (missingDeps.isEmpty) return;

    Get.dialog(
      AlertDialog(
        title: Text('Missing Dependencies'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Some required components are missing. Please install them to use all features:',
                style: Get.textTheme.bodyMedium,
              ),
              SizedBox(height: 16),
              ...missingDeps.map((dep) => _buildDependencyItem(dep)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              checkDependencies(); // Recheck after user potentially installs
            },
            child: Text('Recheck'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  Widget _buildDependencyItem(DependencyInfo dep) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Get.theme.colorScheme.error, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    dep.name,
                    style: Get.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 4),
            Text(dep.description, style: Get.textTheme.bodySmall),
            if (dep.installInstructions != null) ...[
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  dep.installInstructions!,
                  style: Get.textTheme.bodySmall?.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
