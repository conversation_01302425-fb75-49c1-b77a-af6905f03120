import 'package:flutter/material.dart';

/// App-wide constants for consistent styling and spacing
class AppConstants {
  AppConstants._();
  
  // Spacing constants
  static const double spacingXs = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;
  
  // Border radius constants
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXl = 20.0;
  
  // Elevation constants
  static const double elevationS = 1.0;
  static const double elevationM = 2.0;
  static const double elevationL = 4.0;
  static const double elevationXl = 8.0;
  
  // Animation durations
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  
  // Icon sizes
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXl = 48.0;
  
  // Button heights
  static const double buttonHeightS = 36.0;
  static const double buttonHeightM = 48.0;
  static const double buttonHeightL = 56.0;
  
  // Card padding
  static const EdgeInsets cardPadding = EdgeInsets.all(spacingM);
  static const EdgeInsets cardPaddingL = EdgeInsets.all(spacingL);
  
  // Screen padding
  static const EdgeInsets screenPadding = EdgeInsets.all(spacingM);
  static const EdgeInsets screenPaddingHorizontal = EdgeInsets.symmetric(horizontal: spacingM);
  
  // List item padding
  static const EdgeInsets listItemPadding = EdgeInsets.symmetric(
    horizontal: spacingM,
    vertical: spacingS,
  );
  
  // Form field spacing
  static const SizedBox formFieldSpacing = SizedBox(height: spacingM);
  static const SizedBox sectionSpacing = SizedBox(height: spacingL);
  
  // Maximum file size in MB
  static const int maxFileSizeMB = 2000;
  static const int defaultMaxFileSizeMB = 500;
  
  // Supported video formats
  static const List<String> supportedFormats = [
    'mp4',
    'webm',
    'mov',
    'mkv',
    'avi',
  ];
  
  // Quality presets
  static const Map<String, String> qualityPresets = {
    'low': 'Low (Fast)',
    'medium': 'Medium (Balanced)',
    'high': 'High (Slow)',
  };
  
  // Resolution options
  static const List<String> resolutionOptions = [
    'original',
    '1080p',
    '720p',
    '480p',
    '360p',
  ];
  
  // App information
  static const String appName = 'FreeFrame';
  static const String appDescription = 'Free, open source video processing app';
  static const String appTagline = 'Built with Flutter and powered by FFmpeg';
}

/// Extension for consistent spacing widgets
extension SpacingExtension on num {
  SizedBox get verticalSpace => SizedBox(height: toDouble());
  SizedBox get horizontalSpace => SizedBox(width: toDouble());
}

/// Extension for consistent border radius
extension BorderRadiusExtension on num {
  BorderRadius get borderRadius => BorderRadius.circular(toDouble());
}
