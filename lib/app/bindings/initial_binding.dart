import 'package:get/get.dart';
import '../controllers/navigation_controller.dart';
import '../controllers/settings_controller.dart';
import '../controllers/video_processing_controller.dart';
import '../services/dependency_checker.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<DependencyChecker>(Dependency<PERSON>hecker());
    Get.put<NavigationController>(NavigationController(), permanent: true);
    Get.put<SettingsController>(SettingsController(), permanent: true);
    Get.put<VideoProcessingController>(
      VideoProcessingController(),
      permanent: true,
    );
  }
}
