import 'package:get/get.dart';

class NavigationController extends GetxController {
  final _currentIndex = 0.obs;

  int get currentIndex => _currentIndex.value;

  void changeTabIndex(int index) {
    _currentIndex.value = index;
  }

  final List<String> routes = [
    '/home',
    '/convert',
    '/compress',
    '/advanced',
    '/settings',
  ];

  void navigateToTab(int index) {
    if (index >= 0 && index < routes.length) {
      changeTabIndex(index);
      Get.offAllNamed(routes[index]);
    }
  }
}
