import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

enum ProcessingState { idle, processing, completed, error }

class VideoProcessingController extends GetxController {
  final _processingState = ProcessingState.idle.obs;
  final _progress = 0.0.obs;
  final _selectedFile = Rxn<File>();
  final _outputFile = Rxn<File>();
  final _logs = <String>[].obs;
  final _estimatedTime = ''.obs;
  
  ProcessingState get processingState => _processingState.value;
  double get progress => _progress.value;
  File? get selectedFile => _selectedFile.value;
  File? get outputFile => _outputFile.value;
  List<String> get logs => _logs;
  String get estimatedTime => _estimatedTime.value;
  
  bool get isProcessing => processingState == ProcessingState.processing;
  bool get hasSelectedFile => selectedFile != null;
  bool get hasOutputFile => outputFile != null;
  
  Future<void> selectVideoFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.single;

        // On web, path might be null, so we create a mock file for UI purposes
        if (kIsWeb) {
          // For web, we'll store the file info differently since we can't access the file system
          _selectedFile.value = File(file.name); // Use filename as path for display
          _logs.clear();
          addLog('Selected file: ${file.name} (${(file.size / 1024 / 1024).toStringAsFixed(1)} MB)');
          print('DEBUG: Web file selected - ${file.name}, hasSelectedFile: $hasSelectedFile');
        } else {
          // For native platforms, use the actual file path
          if (file.path != null) {
            _selectedFile.value = File(file.path!);
            _logs.clear();
            addLog('Selected file: ${file.name}');
            print('DEBUG: Native file selected - ${file.path}, hasSelectedFile: $hasSelectedFile');
          } else {
            addLog('Error: Could not access file path');
            print('DEBUG: Native file path is null');
          }
        }
      } else {
        addLog('No file selected');
      }
    } catch (e) {
      addLog('Error selecting file: $e');
    }
  }
  
  void addLog(String message) {
    _logs.add('[${DateTime.now().toString().substring(11, 19)}] $message');
  }
  
  void clearLogs() {
    _logs.clear();
  }
  
  void updateProgress(double value) {
    _progress.value = value.clamp(0.0, 1.0);
  }
  
  void setProcessingState(ProcessingState state) {
    _processingState.value = state;
  }
  
  void setEstimatedTime(String time) {
    _estimatedTime.value = time;
  }
  
  void reset() {
    _processingState.value = ProcessingState.idle;
    _progress.value = 0.0;
    _selectedFile.value = null;
    _outputFile.value = null;
    _logs.clear();
    _estimatedTime.value = '';
  }

  // Debug method to test UI reactivity
  void setTestFile() {
    _selectedFile.value = File('test_video.mp4');
    _logs.clear();
    addLog('Test file set for debugging');
    print('DEBUG: Test file set, hasSelectedFile: $hasSelectedFile');
  }
  
  // Platform-specific processing methods will be implemented
  Future<void> processVideo({
    required String outputFormat,
    String? resolution,
    String? quality,
    Map<String, dynamic>? customOptions,
  }) async {
    if (!hasSelectedFile) {
      addLog('No file selected');
      return;
    }
    
    setProcessingState(ProcessingState.processing);
    addLog('Starting video processing...');
    
    try {
      if (kIsWeb) {
        await _processVideoWeb(outputFormat, resolution, quality, customOptions);
      } else {
        await _processVideoNative(outputFormat, resolution, quality, customOptions);
      }
      
      setProcessingState(ProcessingState.completed);
      addLog('Processing completed successfully');
    } catch (e) {
      setProcessingState(ProcessingState.error);
      addLog('Processing failed: $e');
    }
  }
  
  Future<void> _processVideoWeb(String format, String? resolution, String? quality, Map<String, dynamic>? options) async {
    // Import ffmpeg_wasm dynamically for web
    try {
      addLog('Initializing FFmpeg for web...');

      // Simulate web processing for now
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(Duration(milliseconds: 200));
        updateProgress(i / 100.0);
        addLog('Processing: $i%');
        setEstimatedTime('${((100 - i) * 0.2).toInt()}s remaining');
      }

      addLog('Web processing completed (simulated)');
    } catch (e) {
      addLog('Web processing error: $e');
      rethrow;
    }
  }

  Future<void> _processVideoNative(String format, String? resolution, String? quality, Map<String, dynamic>? options) async {
    try {
      addLog('Initializing FFmpeg for native platforms...');

      // Build FFmpeg command
      String command = _buildFFmpegCommand(format, resolution, quality, options);
      addLog('Command: $command');

      // Simulate native processing for now
      for (int i = 0; i <= 100; i += 5) {
        await Future.delayed(Duration(milliseconds: 100));
        updateProgress(i / 100.0);
        if (i % 20 == 0) {
          addLog('Processing frame ${i * 10}...');
        }
        setEstimatedTime('${((100 - i) * 0.1).toInt()}s remaining');
      }

      addLog('Native processing completed (simulated)');
    } catch (e) {
      addLog('Native processing error: $e');
      rethrow;
    }
  }

  String _buildFFmpegCommand(String format, String? resolution, String? quality, Map<String, dynamic>? options) {
    List<String> parts = ['ffmpeg', '-i', 'input'];

    // Video codec based on format
    switch (format.toLowerCase()) {
      case 'mp4':
        parts.addAll(['-c:v', 'libx264']);
        break;
      case 'webm':
        parts.addAll(['-c:v', 'libvpx-vp9']);
        break;
      case 'mov':
        parts.addAll(['-c:v', 'libx264']);
        break;
      default:
        parts.addAll(['-c:v', 'libx264']);
    }

    // Resolution
    if (resolution != null && resolution != 'original') {
      String scale = _getScaleFilter(resolution);
      parts.addAll(['-vf', 'scale=$scale']);
    }

    // Quality settings
    if (quality != null) {
      int crf = _getCrfForQuality(quality);
      parts.addAll(['-crf', crf.toString()]);
    }

    // Audio codec
    if (options?['keepAudio'] == true) {
      parts.addAll(['-c:a', 'aac']);
    } else if (options?['keepAudio'] == false) {
      parts.add('-an');
    }

    // Custom options from advanced mode
    if (options?['videoCodec'] != null) {
      parts[parts.indexOf('-c:v') + 1] = options!['videoCodec'];
    }

    if (options?['audioCodec'] != null && options?['keepAudio'] != false) {
      int audioIndex = parts.indexOf('-c:a');
      if (audioIndex != -1) {
        parts[audioIndex + 1] = options!['audioCodec'];
      }
    }

    if (options?['crf'] != null) {
      int crfIndex = parts.indexOf('-crf');
      if (crfIndex != -1) {
        parts[crfIndex + 1] = options!['crf'].toString();
      }
    }

    if (options?['preset'] != null) {
      parts.addAll(['-preset', options!['preset']]);
    }

    parts.add('output.$format');

    return parts.join(' ');
  }

  String _getScaleFilter(String resolution) {
    switch (resolution) {
      case '1080p':
        return '1920:1080';
      case '720p':
        return '1280:720';
      case '480p':
        return '854:480';
      case '360p':
        return '640:360';
      default:
        return '1280:720';
    }
  }

  int _getCrfForQuality(String quality) {
    switch (quality.toLowerCase()) {
      case 'high':
        return 18;
      case 'medium':
        return 23;
      case 'low':
        return 28;
      default:
        return 23;
    }
  }
}
