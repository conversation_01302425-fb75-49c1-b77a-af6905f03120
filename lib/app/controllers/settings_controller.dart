import 'package:get/get.dart';
import 'package:flutter/foundation.dart';

class SettingsController extends GetxController {
  final _isAdvancedMode = false.obs;
  final _outputQuality = 'medium'.obs;
  final _defaultFormat = 'mp4'.obs;
  final _hardwareAcceleration = true.obs;
  final _maxFileSize = 500.obs; // MB
  
  bool get isAdvancedMode => _isAdvancedMode.value;
  String get outputQuality => _outputQuality.value;
  String get defaultFormat => _defaultFormat.value;
  bool get hardwareAcceleration => _hardwareAcceleration.value;
  int get maxFileSize => _maxFileSize.value;
  
  void toggleAdvancedMode() {
    _isAdvancedMode.value = !_isAdvancedMode.value;
  }
  
  void setOutputQuality(String quality) {
    _outputQuality.value = quality;
  }
  
  void setDefaultFormat(String format) {
    _defaultFormat.value = format;
  }
  
  void toggleHardwareAcceleration() {
    _hardwareAcceleration.value = !_hardwareAcceleration.value;
  }
  
  void setMaxFileSize(int size) {
    _maxFileSize.value = size;
  }
  
  // Platform detection helpers
  bool get isWeb => kIsWeb;
  bool get isMobile => GetPlatform.isMobile;
  bool get isDesktop => GetPlatform.isDesktop;
  
  // Quality presets
  Map<String, String> get qualityPresets => {
    'low': 'Low (Fast)',
    'medium': 'Medium (Balanced)', 
    'high': 'High (Slow)',
  };
  
  // Format options
  List<String> get supportedFormats => [
    'mp4',
    'webm', 
    'mov',
    'mkv',
    'avi',
  ];
}
